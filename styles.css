/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    flex-direction: row-reverse;
}

.logo {
    font-size: 2rem;
    font-weight: bold;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #fbbf24 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
    font-family: 'Arial', sans-serif;
    letter-spacing: 1px;
    position: relative;
    padding: 0.5rem 1rem;
    border-radius: 12px;
    background-color: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(102, 126, 234, 0.2);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.15);
    transition: all 0.3s ease;
    cursor: pointer;
}

.logo::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #fbbf24 100%);
    border-radius: 12px;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.logo:hover {
    background: linear-gradient(135deg, #fbbf24 0%, #764ba2 50%, #667eea 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.25);
    border-color: rgba(102, 126, 234, 0.4);
    transform: translateY(-1px);
}

.logo:hover::before {
    opacity: 0.1;
}

.login-link {
    background: linear-gradient(45deg, #667eea, #764ba2) !important;
    color: white !important;
    border-radius: 5px !important;
    font-weight: 600 !important;
    box-shadow: 0 3px 15px rgba(102, 126, 234, 0.3) !important;
}

.login-link:hover {
    background: linear-gradient(45deg, #764ba2, #667eea) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 5px 20px rgba(102, 126, 234, 0.4) !important;
}

.login-link.logged-in {
    background: linear-gradient(45deg, #4CAF50, #45a049) !important;
    color: white !important;
    pointer-events: none !important;
}

.login-link.logged-in:hover {
    background: linear-gradient(45deg, #4CAF50, #45a049) !important;
    transform: none !important;
}

/* User Menu Dropdown */
.user-menu {
    position: relative;
    display: inline-block;
}

.user-menu-btn {
    background: linear-gradient(45deg, #4CAF50, #45a049) !important;
    color: white !important;
    border-radius: 5px !important;
    font-weight: 600 !important;
    box-shadow: 0 3px 15px rgba(76, 175, 80, 0.3) !important;
    text-decoration: none !important;
    padding: 0.5rem 1rem !important;
    transition: all 0.3s ease !important;
    cursor: pointer !important;
    border: none !important;
    font-size: inherit !important;
}

.user-menu-btn:hover {
    background: linear-gradient(45deg, #45a049, #4CAF50) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 5px 20px rgba(76, 175, 80, 0.4) !important;
}

.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    min-width: 200px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    margin-top: 0.5rem;
}

.user-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.user-dropdown-item {
    display: block;
    padding: 1rem 1.5rem;
    color: #333;
    text-decoration: none;
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    cursor: pointer;
}

.user-dropdown-item:first-child {
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}

.user-dropdown-item:last-child {
    border-bottom: none;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}

.user-dropdown-item:hover {
    background: #f5f5f5;
}

.user-dropdown-item.status {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    color: white;
    font-weight: 600;
    pointer-events: none;
}

.user-dropdown-item.logout {
    color: #f44336;
    font-weight: 500;
}

.user-dropdown-item.logout:hover {
    background: rgba(244, 67, 54, 0.1);
}

.user-dropdown-item.admin-link {
    color: #667eea;
    font-weight: 500;
    cursor: pointer;
}

.user-dropdown-item.admin-link:hover {
    background: rgba(102, 126, 234, 0.1);
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-links a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 0.5rem 1rem;
    border-radius: 25px;
}

.nav-links a:hover {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    transform: translateY(-2px);
}

/* Mobile Menu Button */
.mobile-menu-btn {
    display: none;
    flex-direction: column;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
}

.mobile-menu-btn span {
    width: 25px;
    height: 3px;
    background: #667eea;
    margin: 3px 0;
    transition: 0.3s;
    border-radius: 2px;
}

/* Sidebar Styles */
.sidebar {
    position: fixed;
    top: 0;
    right: -300px;
    width: 300px;
    height: 100vh;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
    box-shadow: -5px 0 20px rgba(0, 0, 0, 0.1);
    transition: right 0.3s ease;
    z-index: 2000;
    padding: 0;
}

.sidebar.active {
    right: 0;
}

.sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid rgba(102, 126, 234, 0.2);
}

.sidebar-logo {
    font-size: 1.5rem;
    font-weight: bold;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #fbbf24 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: 1px;
    position: relative;
    padding: 0.4rem 0.8rem;
    border-radius: 10px;
    background-color: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(102, 126, 234, 0.2);
    box-shadow: 0 3px 12px rgba(102, 126, 234, 0.15);
    transition: all 0.3s ease;
    cursor: pointer;
}

.sidebar-logo:hover {
    background: linear-gradient(135deg, #fbbf24 0%, #764ba2 50%, #667eea 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    box-shadow: 0 5px 18px rgba(102, 126, 234, 0.25);
    border-color: rgba(102, 126, 234, 0.4);
    transform: translateY(-1px);
}

.close-btn {
    background: none;
    border: none;
    font-size: 2rem;
    color: #667eea;
    cursor: pointer;
    padding: 0;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: rgba(102, 126, 234, 0.1);
}

.sidebar-nav {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar-nav li {
    border-bottom: 1px solid rgba(102, 126, 234, 0.1);
}

.sidebar-nav a {
    display: block;
    padding: 1.2rem 1.5rem;
    color: #333;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.sidebar-nav a:hover {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
}

.sidebar-login-link {
    background: linear-gradient(45deg, #667eea, #764ba2) !important;
    color: white !important;
    font-weight: 600 !important;
    text-align: center !important;
    margin: 1rem !important;
    border-radius: 5px !important;
}

.sidebar-login-link:hover {
    background: linear-gradient(45deg, #764ba2, #667eea) !important;
    transform: scale(1.02) !important;
}

.sidebar-login-link.logged-in {
    background: linear-gradient(45deg, #4CAF50, #45a049) !important;
    color: white !important;
    pointer-events: none !important;
}

.sidebar-login-link.logged-in:hover {
    background: linear-gradient(45deg, #4CAF50, #45a049) !important;
    transform: none !important;
}

/* Sidebar User Menu */
.sidebar-user-menu {
    margin: 1rem;
}

.sidebar-user-status {
    background: linear-gradient(45deg, #4CAF50, #45a049) !important;
    color: white !important;
    font-weight: 600 !important;
    text-align: center !important;
    padding: 1rem !important;
    border-radius: 5px !important;
    margin-bottom: 0.5rem !important;
    pointer-events: none !important;
}

.sidebar-logout-btn {
    background: rgba(244, 67, 54, 0.1) !important;
    color: #f44336 !important;
    font-weight: 500 !important;
    text-align: center !important;
    padding: 1rem !important;
    border-radius: 5px !important;
    text-decoration: none !important;
    display: block !important;
    transition: all 0.3s ease !important;
    cursor: pointer !important;
}

.sidebar-logout-btn:hover {
    background: rgba(244, 67, 54, 0.2) !important;
    transform: scale(1.02) !important;
}

/* Overlay */
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1500;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Main Content Styles */
main {
    margin-top: 100px;
    padding: 2rem 0;
}

.hero-section {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 20px;
    padding: 3rem 0 0 0;
    margin-bottom: 3rem;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    text-align: center;
    overflow: hidden;
}

.hero-title {
    font-size: 3rem;
    color: #fbbf24;
    background: #1e3a8a;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    font-family: 'Arial', sans-serif;
    font-weight: bold;
    letter-spacing: 2px;
    padding: 1.5rem 3rem;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(30, 58, 138, 0.3);
}

.hero-subtitle {
    font-size: 1.5rem;
    color: #764ba2;
    margin-bottom: 2rem;
    padding: 0 3rem;
}

.company-image-container {
    width: calc(100% - 4rem);
    max-width: 800px;
    height: 400px;
    margin: 2rem auto 0 auto;
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
}

.company-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.image-overlay-top {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 60px;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0.6), transparent);
    pointer-events: none;
    z-index: 1;
}

.image-overlay-bottom {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 60px;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.6), transparent);
    pointer-events: none;
    z-index: 1;
}

/* Moving Text Banner */
.moving-text-banner {
    background: linear-gradient(45deg, #667eea, #764ba2);
    padding: 1rem 0;
    overflow: hidden;
    position: relative;
    margin: 2rem 0 0 0;
    border-radius: 25px;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.2);
    height: 60px;
    display: flex !important;
    align-items: center;
    visibility: visible !important;
    opacity: 1 !important;
}

.moving-text-banner::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 100px;
    background: linear-gradient(to right, rgba(102, 126, 234, 0.8), transparent);
    z-index: 2;
    border-radius: 25px 0 0 25px;
}

.moving-text-banner::after {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 100px;
    background: linear-gradient(to left, rgba(118, 75, 162, 0.8), transparent);
    z-index: 2;
    border-radius: 0 25px 25px 0;
}

.moving-text-container {
    width: 100%;
    overflow: hidden;
}

.moving-text {
    display: flex !important;
    white-space: nowrap;
    animation: moveText 30s linear infinite;
    gap: 5rem;
    visibility: visible !important;
    opacity: 1 !important;
}

.moving-text span {
    color: white;
    font-size: 1.3rem;
    font-weight: 700;
    text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.4);
    letter-spacing: 1.2px;
    display: inline-block;
    min-width: max-content;
    position: relative;
    z-index: 3;
}

@keyframes moveText {
    0% {
        transform: translateX(100%);
    }
    100% {
        transform: translateX(-100%);
    }
}



/* About Section Styles */
.about-section {
    padding: 2rem 0;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    margin: 1rem 0;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.about-title {
    text-align: center;
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 2rem;
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

.about-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
    padding: 0 2rem;
}

.about-description {
    font-size: 1.2rem;
    line-height: 1.8;
    color: #555;
    margin: 0;
    text-align: justify;
    text-align-last: center;
}

.about-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 0;
    color: #666;
}

.about-loading .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

.about-loading p {
    font-size: 1.1rem;
    margin: 0;
    color: #888;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Gallery Section Styles */
.gallery-section {
    margin-top: 2rem;
}

/* Featured Badge */
.featured-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #333;
    padding: 0.3rem 0.6rem;
    border-radius: 15px;
    font-size: 0.7rem;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
    z-index: 2;
}

/* Category Badge */
.category-badge {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
    display: inline-block;
}

/* Gallery Item Meta */
.gallery-item-meta {
    margin: 0.5rem 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* No Gallery Message */
.no-gallery-message {
    text-align: center;
    padding: 3rem;
    color: #666;
    font-size: 1.1rem;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 15px;
    margin: 2rem 0;
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-top: 1.5rem;
}

.gallery-item {
    border-radius: 10px;
    overflow: hidden;
    transition: transform 0.3s ease;
}

.gallery-item:hover {
    transform: translateY(-5px);
}

.gallery-image {
    width: 100%;
    height: 180px;
    object-fit: cover;
    display: block;
    transition: transform 0.3s ease;
}

.gallery-item:hover .gallery-image {
    transform: scale(1.02);
}

/* Main Gallery Section Styles */
.gallery-main-section {
    padding: 4rem 0;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    margin: 3rem 0;
    border-radius: 20px;
}

.gallery-main-title {
    text-align: center;
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 3rem;
    font-weight: 700;
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.gallery-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Gallery Content */
.gallery-content {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.gallery-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid rgba(102, 126, 234, 0.2);
    flex-wrap: wrap;
    gap: 1rem;
}

.current-category-title {
    font-size: 1.5rem;
    color: #333;
    margin: 0;
    font-weight: 600;
}

.gallery-controls {
    display: flex;
    align-items: center;
    justify-content: center;
}

.gallery-pagination {
    display: flex;
    align-items: center;
}

.page-info {
    font-size: 1rem;
    color: #666;
    font-weight: 500;
    background: rgba(102, 126, 234, 0.1);
    padding: 0.5rem 1rem;
    border-radius: 20px;
}

.page-info span {
    color: #667eea;
    font-weight: 700;
}



/* Gallery Main Grid */
.gallery-main-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    transition: all 0.3s ease;
    min-height: 600px;
}

.gallery-main-grid.list-view {
    grid-template-columns: 1fr;
    gap: 2rem;
}

.gallery-main-item {
    display: flex;
    flex-direction: column;
    border-radius: 20px;
    overflow: hidden;
    transition: all 0.3s ease;
    background: white;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border: 2px solid #f0f2f5;
    min-height: auto;
    height: fit-content;
}

.gallery-main-item:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.25);
    border-color: #667eea;
}

.gallery-image-container {
    position: relative;
    overflow: hidden;
    background: #f8f9fa;
    height: 240px;
    border-radius: 15px 15px 0 0;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    will-change: transform;
}

.gallery-main-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    transition: all 0.3s ease;
    filter: brightness(1) contrast(1.08) saturate(1.05);
    display: block;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: high-quality;
    image-rendering: -moz-crisp-edges;
    image-rendering: crisp-edges;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
}

.gallery-main-item:hover .gallery-main-image {
    transform: scale(1.02) translateZ(0);
    filter: brightness(1.02) contrast(1.12) saturate(1.08);
    -webkit-filter: brightness(1.02) contrast(1.12) saturate(1.08);
}

.gallery-item-info {
    padding: 1.5rem;
    background: white;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.gallery-item-info h4 {
    margin: 0 0 0.8rem 0;
    font-size: 1.3rem;
    font-weight: 700;
    line-height: 1.3;
    color: #333;
    text-align: right;
}

.gallery-item-info p {
    margin: 0 0 1rem 0;
    font-size: 1rem;
    line-height: 1.6;
    color: #666;
    text-align: right;
    flex-grow: 1;
}

/* Gallery Loading */
.gallery-loading {
    text-align: center;
    padding: 3rem;
    color: #666;
}

.gallery-loading .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(102, 126, 234, 0.2);
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}



/* Branches Section Styles */
.branches-section {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 20px;
    padding: 3rem;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

.branches-title {
    font-size: 2.5rem;
    color: #667eea;
    margin-bottom: 2rem;
    text-align: center;
}

.branches-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.branch-card {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    transition: all 0.3s ease;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.branch-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
}

.branch-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.branch-card a {
    color: white;
    text-decoration: none;
    font-weight: bold;
    display: inline-block;
    margin-top: 1rem;
    padding: 0.5rem 1.5rem;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    transition: all 0.3s ease;
}

.branch-card a:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

/* Section Separator */
.section-separator {
    height: 80px;
    background: linear-gradient(45deg, transparent 0%, rgba(102, 126, 234, 0.1) 50%, transparent 100%);
    margin: 3rem 0;
    position: relative;
}

.section-separator::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100px;
    height: 2px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 2px;
}

/* Contact Section Styles */
.contact-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 4rem 3rem;
    margin-bottom: 3rem;
    box-shadow: 0 15px 50px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(102, 126, 234, 0.1);
}

.contact-title {
    font-size: 2.5rem;
    color: #667eea;
    margin-bottom: 2rem;
    text-align: center;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: start;
}

.contact-info h3 {
    color: #667eea;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1.5rem;
    padding: 1.5rem;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 15px;
    border: 1px solid rgba(102, 126, 234, 0.1);
    transition: all 0.3s ease;
}

.contact-item:hover {
    background: rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
}

.contact-icon {
    font-size: 2rem;
    margin-left: 1rem;
    flex-shrink: 0;
}

.contact-details {
    flex: 1;
}

.contact-details strong {
    color: #667eea;
    display: block;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.contact-details span {
    display: block;
    color: #555;
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

.contact-action-btn {
    display: inline-block;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    margin-top: 0.5rem;
}

.contact-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.contact-form-container h3 {
    color: #667eea;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
}

.contact-form .form-group {
    margin-bottom: 1.5rem;
}

.contact-form label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #333;
}

.contact-form input,
.contact-form textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #ddd;
    border-radius: 10px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    font-family: inherit;
}

.contact-form input:focus,
.contact-form textarea:focus {
    outline: none;
    border-color: #667eea;
}

.contact-submit-btn {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 25px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.contact-submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.contact-submit-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

/* Footer Styles */
footer {
    background: rgba(0, 0, 0, 0.8);
    color: white;
    text-align: center;
    padding: 2rem 0;
    margin-top: 3rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    /* Hide desktop navigation and show mobile menu button */
    .desktop-nav {
        display: none;
    }

    .mobile-menu-btn {
        display: flex;
    }

    .hero-title {
        font-size: 2rem;
        padding: 1rem 1.5rem;
        color: #fbbf24;
        background: #1e3a8a;
        border-radius: 12px;
        box-shadow: 0 6px 20px rgba(30, 58, 138, 0.3);
    }

    .company-image-container {
        width: calc(100% - 2rem);
        height: 300px;
        margin: 1.5rem auto 0 auto;
    }

    .image-overlay-top,
    .image-overlay-bottom {
        height: 40px;
    }

    .hero-subtitle {
        font-size: 1.2rem;
        padding: 0 1.5rem;
    }

    .branches-grid {
        grid-template-columns: 1fr;
    }

    .gallery-grid {
        grid-template-columns: 1fr;
        gap: 0.8rem;
    }

    .gallery-image {
        height: 150px;
    }

    /* Gallery Main Section Responsive */
    .gallery-main-section {
        padding: 2rem 0;
        margin: 2rem 0;
    }

    .gallery-main-title {
        font-size: 2rem;
        margin-bottom: 2rem;
    }

    .gallery-container {
        padding: 0 1rem;
    }

    .gallery-content {
        padding: 1.5rem;
    }

    .moving-text span {
        font-size: 1.1rem;
        letter-spacing: 1px;
    }

    .moving-text-banner {
        padding: 0.8rem 0;
        margin: 0.8rem 1rem;
        border-radius: 20px;
        height: 50px;
    }

    .moving-text-banner::before,
    .moving-text-banner::after {
        width: 80px;
    }

    .gallery-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .current-category-title {
        text-align: center;
        font-size: 1.3rem;
    }

    .gallery-controls {
        justify-content: center;
    }

    .gallery-main-grid {
        grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
        gap: 1.3rem;
    }

    .gallery-main-image {
        height: 200px;
    }

    .gallery-image-container {
        height: 200px;
    }

    .gallery-item-info {
        padding: 1.2rem;
    }

    .gallery-item-info h4 {
        font-size: 1.1rem;
    }

    .gallery-item-info p {
        font-size: 0.9rem;
    }

    .gallery-item-actions {
        padding: 0.7rem 1.2rem;
        margin-top: 1rem;
    }

    .view-text {
        font-size: 0.85rem;
    }

    .gallery-categories {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 0.5rem;
        margin-bottom: 1.5rem;
    }

    .gallery-category {
        padding: 0.8rem;
        text-align: center;
        flex-direction: column;
        gap: 0.3rem;
    }

    .category-icon {
        margin: 0;
        font-size: 1.5rem;
    }

    .category-name {
        font-size: 0.8rem;
    }

    .gallery-stats {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        padding: 1rem;
    }

    .logo {
        font-size: 1.5rem;
    }



    .branches-title,
    .contact-title,
    .about-title {
        font-size: 2rem;
    }

    .about-section {
        padding: 1.5rem 0;
        margin: 0.8rem 0;
    }

    .about-description {
        font-size: 1.1rem;
        text-align: center;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .contact-section {
        padding: 2rem 1.5rem;
    }

    .contact-item {
        flex-direction: column;
        text-align: center;
        padding: 1rem;
    }

    .contact-icon {
        margin: 0 0 1rem 0;
    }

    .contact-details strong {
        font-size: 1rem;
    }

    .section-separator {
        height: 60px;
        margin: 2rem 0;
    }

    /* Adjust sidebar for smaller screens */
    .sidebar {
        width: 280px;
        right: -280px;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 1.5rem;
        padding: 0.8rem 1rem;
        color: #fbbf24;
        background: #1e3a8a;
        border-radius: 10px;
        box-shadow: 0 4px 15px rgba(30, 58, 138, 0.3);
    }

    .company-image-container {
        width: calc(100% - 1rem);
        height: 250px;
        margin: 1rem auto 0 auto;
    }

    .image-overlay-top,
    .image-overlay-bottom {
        height: 30px;
    }

    .hero-subtitle {
        font-size: 1rem;
        padding: 0 1rem;
    }



    .company-image {
        height: 250px;
        max-width: 100%;
        border-radius: 0 0 10px 10px;
        margin: 1rem 0 0 0;
    }

    .gallery-image {
        height: 120px;
    }

    .gallery-section {
        margin-top: 1.5rem;
    }

    /* Gallery Main Section for Small Screens */
    .gallery-main-section {
        padding: 1.5rem 0;
        margin: 1.5rem 0;
        border-radius: 15px;
    }

    .gallery-main-title {
        font-size: 1.8rem;
        margin-bottom: 1.5rem;
        padding: 0 1rem;
    }

    .gallery-container {
        padding: 0 0.5rem;
        gap: 1rem;
    }

    .gallery-sidebar {
        padding: 1rem;
    }

    .gallery-content {
        padding: 1rem;
    }

    .gallery-main-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .gallery-main-image {
        height: 180px;
    }

    .gallery-image-container {
        height: 180px;
    }

    .gallery-item-info {
        padding: 1rem;
    }

    .gallery-item-info h4 {
        font-size: 1rem;
    }

    .gallery-item-info p {
        font-size: 0.85rem;
    }

    .gallery-item-actions {
        padding: 0.6rem 1rem;
        margin-top: 0.8rem;
    }

    .view-icon {
        font-size: 1.1rem;
    }

    .view-text {
        font-size: 0.8rem;
    }



    .pagination-controls {
        flex-direction: column;
        gap: 1.5rem;
        padding: 1.5rem;
    }

    .pagination-btn {
        padding: 0.7rem 1.2rem;
        font-size: 0.9rem;
    }

    .page-number {
        width: 35px;
        height: 35px;
    }

    .moving-text span {
        font-size: 1rem;
        letter-spacing: 0.8px;
    }

    .moving-text-banner {
        padding: 0.6rem 0;
        margin: 0.8rem 0.5rem;
        border-radius: 15px;
        height: 40px;
    }

    .moving-text-banner::before,
    .moving-text-banner::after {
        width: 60px;
    }

    .moving-text {
        animation: moveText 25s linear infinite;
        gap: 3rem;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .stat-label {
        font-size: 0.75rem;
    }

    .gallery-header {
        margin-bottom: 1rem;
        padding-bottom: 0.8rem;
    }

    .current-category-title {
        font-size: 1.1rem;
    }

    .gallery-control-btn {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }

    /* Image Modal Responsive */
    .image-modal-content {
        max-width: 95%;
        max-height: 95%;
    }

    .image-modal-img {
        max-height: 60vh;
    }

    .image-modal-info {
        padding: 1.5rem;
    }

    .image-modal-info h3 {
        font-size: 1.3rem;
    }

    .image-modal-info p {
        font-size: 0.9rem;
    }

    .image-modal-close {
        top: 10px;
        right: 15px;
        width: 35px;
        height: 35px;
        font-size: 1.5rem;
    }

    /* Adjust sidebar for very small screens */
    .sidebar {
        width: 250px;
        right: -250px;
    }

    .sidebar-header {
        padding: 1rem;
    }

    .sidebar-nav a {
        padding: 1rem;
    }


}

/* Dynamic Content Animations */
.content-updating {
    transition: all 0.3s ease;
    transform: scale(1.02);
    box-shadow: 0 0 15px rgba(102, 126, 234, 0.4);
}

.firebase-image {
    animation: fadeInUp 0.5s ease-out;
}

/* Gallery Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.gallery-main-item {
    animation: fadeInUp 0.6s ease-out;
}

.gallery-main-item:nth-child(2) {
    animation-delay: 0.1s;
}

.gallery-main-item:nth-child(3) {
    animation-delay: 0.2s;
}

.gallery-main-item:nth-child(4) {
    animation-delay: 0.3s;
}

.gallery-category {
    animation: slideInRight 0.4s ease-out;
}

.gallery-category:nth-child(2) {
    animation-delay: 0.1s;
}

.gallery-category:nth-child(3) {
    animation-delay: 0.2s;
}

.gallery-category:nth-child(4) {
    animation-delay: 0.3s;
}

.gallery-category:nth-child(5) {
    animation-delay: 0.4s;
}

.stat-number {
    animation: pulse 2s infinite;
}

/* Smooth scrolling for gallery navigation */
html {
    scroll-behavior: smooth;
}

/* Gallery section scroll padding */
#gallery {
    scroll-margin-top: 80px;
}

/* Image Modal Styles */
.image-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    z-index: 3000;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.image-modal.active {
    opacity: 1;
}

.image-modal-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
    transform: scale(0.8);
    transition: transform 0.3s ease;
}

.image-modal.active .image-modal-content {
    transform: scale(1);
}

.image-modal-close {
    position: absolute;
    top: 15px;
    right: 20px;
    font-size: 2rem;
    color: white;
    background: rgba(0, 0, 0, 0.5);
    border: none;
    cursor: pointer;
    z-index: 3001;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.image-modal-close:hover {
    background: rgba(0, 0, 0, 0.8);
    transform: scale(1.1);
}

.image-modal-img {
    width: 100%;
    max-height: 70vh;
    object-fit: contain;
    display: block;
}

.image-modal-info {
    padding: 2rem;
    text-align: center;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.image-modal-info h3 {
    margin: 0 0 1rem 0;
    font-size: 1.5rem;
    color: #333;
    font-weight: 600;
}

.image-modal-info p {
    margin: 0;
    color: #666;
    font-size: 1rem;
    line-height: 1.6;
}

/* Gallery Item Actions */
.gallery-item-actions {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.8rem;
    margin-top: auto;
    padding: 0.8rem 1.5rem;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 8px;
    border: none;
    transition: all 0.3s ease;
    cursor: pointer;
}

.gallery-item-actions:hover {
    background: linear-gradient(45deg, #764ba2, #667eea);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.view-icon {
    font-size: 1.2rem;
    color: white;
}

.view-text {
    font-size: 0.95rem;
    font-weight: 600;
    color: white;
}

.gallery-main-item {
    cursor: pointer;
}

/* Pagination Controls */
.pagination-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-top: 3rem;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.pagination-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.8rem 1.5rem;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    font-size: 0.95rem;
}

.pagination-btn:hover {
    background: linear-gradient(45deg, #764ba2, #667eea);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.pagination-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.pagination-numbers {
    display: flex;
    gap: 0.5rem;
}

.page-number {
    width: 40px;
    height: 40px;
    border: 2px solid #667eea;
    background: white;
    color: #667eea;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.page-number:hover {
    background: rgba(102, 126, 234, 0.1);
    transform: scale(1.05);
}

.page-number.active {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border-color: #667eea;
}

/* Gallery Item Hover Effects */
.gallery-main-item:hover .gallery-item-info h4 {
    color: #667eea;
    transform: translateY(-1px);
}

.gallery-main-item:hover .gallery-item-info p {
    color: #555;
}

.gallery-main-item:hover .gallery-item-actions {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

/* Image Modal Navigation */
.image-modal-nav {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    transform: translateY(-50%);
    display: flex;
    justify-content: space-between;
    padding: 0 20px;
    pointer-events: none;
    z-index: 3002;
}

.modal-nav-btn {
    background: rgba(0, 0, 0, 0.5);
    border: none;
    color: white;
    font-size: 2rem;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    pointer-events: auto;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-nav-btn:hover {
    background: rgba(0, 0, 0, 0.8);
    transform: scale(1.1);
}

.modal-nav-btn.prev {
    margin-right: auto;
}

.modal-nav-btn.next {
    margin-left: auto;
}

/* Modal Counter */
.modal-counter {
    margin-top: 1rem;
    padding: 0.5rem 1rem;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 20px;
    display: inline-block;
}

#modalCounter {
    font-size: 0.9rem;
    color: #667eea;
    font-weight: 600;
}

/* Modal Image Transitions */
.image-modal-img {
    transition: opacity 0.3s ease;
}

/* Mobile Modal Navigation */
@media (max-width: 768px) {
    .image-modal-nav {
        padding: 0 10px;
    }

    .modal-nav-btn {
        width: 40px;
        height: 40px;
        font-size: 1.5rem;
    }

    .image-modal-close {
        top: 10px;
        right: 10px;
        width: 30px;
        height: 30px;
        font-size: 1.2rem;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Real-time update indicator */
.update-indicator {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    z-index: 1001;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
}

.update-indicator.show {
    opacity: 1;
    transform: translateX(0);
}

/* Loading states */
.content-loading {
    opacity: 0.7;
    pointer-events: none;
}

.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* No data messages */
.no-data-message {
    text-align: center;
    padding: 3rem 2rem;
    background: rgba(102, 126, 234, 0.05);
    border: 2px dashed rgba(102, 126, 234, 0.2);
    border-radius: 15px;
    margin: 2rem 0;
    color: #666;
    font-style: italic;
    transition: all 0.3s ease;
}

.no-data-message:hover {
    background: rgba(102, 126, 234, 0.1);
    border-color: rgba(102, 126, 234, 0.3);
}

.no-data-message p {
    margin: 0;
    font-size: 1.1rem;
    line-height: 1.6;
}

.no-data-message.hidden {
    display: none;
}

/* Force visible no-data messages to stay visible */
.no-data-message[data-force-visible="true"] {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.no-data-message[data-force-visible="true"].hidden {
    display: block !important;
}
